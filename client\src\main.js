import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

// Import PrimeVue
import PrimeVue from 'primevue/config'
// Import PrimeIcons
import 'primeicons/primeicons.css'
import Aura from '@primeuix/themes/aura'
// import Lara from '@primeuix/themes/lara';

// Import PrimeVue components
import Button from 'primevue/button'
import Card from 'primevue/card'
import Badge from 'primevue/badge'
import Avatar from 'primevue/avatar'
import Tooltip from 'primevue/tooltip'
import Ripple from 'primevue/ripple'
import Popover from 'primevue/popover'
import Menu from 'primevue/menu'
import Menubar from 'primevue/menubar'
import InputText from 'primevue/inputtext'
import Toast from 'primevue/toast'
import ToastService from 'primevue/toastservice'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Dialog from 'primevue/dialog'
import Textarea from 'primevue/textarea'

import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'

const app = createApp(App)

app.use(createPinia())
app.use(router)

// Initialize auth store after pinia is set up
const authStore = useAuthStore()
authStore.initializeAuth()
app.use(ToastService)
// Use PrimeVue
app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
      darkModeSelector: false,
    },
  },
})

// Register PrimeVue components globally
app.component('Button', Button)
app.component('Card', Card)
app.component('Badge', Badge)
app.component('Avatar', Avatar)
app.component('Popover', Popover)
app.component('Menu', Menu)
app.component('Menubar', Menubar)
app.component('InputText', InputText)
app.component('Toast', Toast)
app.component('DataTable', DataTable)
app.component('Column', Column)
app.component('Dialog', Dialog)
app.component('Textarea', Textarea)

// Register directives
app.directive('tooltip', Tooltip)
app.directive('ripple', Ripple)

app.mount('#app')
