import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { api } from '@/utils/api'

export const useMenuStore = defineStore('menu', () => {
  // State
  const menuData = ref([])
  const isMenuLoaded = ref(false)

  // Getters
  const menuItems = computed(() => menuData.value)
  const isLoaded = computed(() => isMenuLoaded.value)

  // Helper function to transform menu data for PrimeVue Menubar
  const transformMenuData = (menuArray) => {
    if (!menuArray || !Array.isArray(menuArray)) return []

    // Create a map for quick lookup
    const menuMap = new Map()
    const rootMenus = []

    // First pass: create all menu items
    menuArray.forEach(item => {
      const menuItem = {
        id: item.MenuID,
        label: item.MenuName,
        icon: item.MenuIcon ? `pi pi-${item.MenuIcon}` : null,
        url: item.MenuUrl,
        screenUrl: item.ScreenUrl,
        parentId: item.ParentMenuID,
        description: item.MenuDescription,
        rwx: item.RWX,
        items: [],
        command: item.MenuUrl ? () => {
          // Handle navigation - this will be implemented in the component
          if (window.router) {
            window.router.push(item.MenuUrl)
          }
        } : undefined
      }
      menuMap.set(item.MenuID, menuItem)
    })

    // Second pass: build hierarchy
    menuArray.forEach(item => {
      const menuItem = menuMap.get(item.MenuID)
      if (item.ParentMenuID && menuMap.has(item.ParentMenuID)) {
        // This is a submenu
        const parent = menuMap.get(item.ParentMenuID)
        parent.items.push(menuItem)
      } else {
        // This is a root menu
        rootMenus.push(menuItem)
      }
    })

    // Clean up empty items arrays and sort by order if available
    const cleanMenu = (menu) => {
      if (menu.items && menu.items.length === 0) {
        delete menu.items
      } else if (menu.items) {
        menu.items.forEach(cleanMenu)
      }
      return menu
    }

    return rootMenus.map(cleanMenu)
  }

  // Actions
  const loadMenu = async () => {
    try {
      const response = await api.call('Arch_SelMenu')
      
      if (response.success && response.data) {
        menuData.value = response.data
        isMenuLoaded.value = true

        // Store in localStorage for persistence
        localStorage.setItem('menuData', JSON.stringify(response.data))
        localStorage.setItem('isMenuLoaded', 'true')

        return { success: true, data: response.data }
      } else {
        throw new Error(response.message || 'Failed to load menu')
      }
    } catch (error) {
      console.error('Menu loading error:', error)
      isMenuLoaded.value = false
      return {
        success: false,
        message: error.message || 'Failed to load menu. Please try again.'
      }
    }
  }

  const clearMenu = () => {
    menuData.value = []
    isMenuLoaded.value = false

    // Clear localStorage
    localStorage.removeItem('menuData')
    localStorage.removeItem('isMenuLoaded')
  }

  const initializeMenu = () => {
    // Check if menu is already loaded (from localStorage)
    const storedMenuData = localStorage.getItem('menuData')
    const storedMenuLoaded = localStorage.getItem('isMenuLoaded')

    if (storedMenuData && storedMenuLoaded === 'true') {
      try {
        menuData.value = JSON.parse(storedMenuData)
        isMenuLoaded.value = true
      } catch (error) {
        console.error('Error parsing stored menu data:', error)
        clearMenu()
      }
    }
  }

  // Computed property for transformed menu items
  const transformedMenuItems = computed(() => {
    return transformMenuData(menuData.value)
  })

  return {
    // State
    menuData,
    isMenuLoaded,
    // Getters
    menuItems,
    isLoaded,
    transformedMenuItems,
    // Actions
    loadMenu,
    clearMenu,
    initializeMenu
  }
})
