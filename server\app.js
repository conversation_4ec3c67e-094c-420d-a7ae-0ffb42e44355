var express = require("express");
const app = express();
var bodyParser = require("body-parser");
require('dotenv').config()

const {authMiddleware} = require('./api/auth');
var api = require("./api/call");
var dinsos = require("./api/dinsos");
var appex = require("./api/appex");
var reports = require("./api/reports");
var batch = require("./api/batch");
var otherReports = require("./api/reports/others")
var backlogReports = require("./api/reports/backlog")
var db = require("./common/db");
// var {websocket} = require("./common/ws");
var timeout = require("connect-timeout");
var cookieParser = require("cookie-parser");
const telegram = require("./common/telegram");
const gistaru = require("./api/thirdparty/gistaru");
const igahp = require("./api/thirdparty/igahp");
const docs = require("./api/reports/document");

const PORT = 8001;

app.use(bodyParser.json());
app.use(cookieParser(process.env.CSECRET));

// process.env.DEVELOPMENT = true;

app.set("etag", false);

const cors = require("cors");
app.use(
  cors({
    origin: function (origin, callback) {
      if (!origin) return callback(null, true);
      // console.log('Origin: ', origin)
      // if (allowedOrigins.indexOf(origin) === -1) {
      //   var msg = "The CORS policy for this site does not " + "allow access from the specified Origin.";
      //   return callback(new Error(msg), false);
      // }
      return callback(null, true);
    },
    credentials: true,
    allowedHeaders: "Origin,X-Requested-With,Content-Type,Accept,Authorization,X-Device-Id",
    methods: "GET,PUT,POST,DELETE,OPTIONS"
  })
);
app.use("/uploads", authMiddleware, express.static("uploads"));

app.locals.telegram = telegram

app.use("/api", api);
app.use("/api/dinsos", dinsos);
app.use("/api/gistaru", gistaru);
app.use("/api/igahp", igahp);
app.use("/api/docs", docs);
app.use("/api/notifications", require("./api/notifications"));
app.use("/reports", reports);
app.use("/reports/others", otherReports);
app.use("/report/backlog", backlogReports);
app.use("/report", reports);
app.use("/batch", batch);
app.use("/", appex);
app.use(timeout("30000s"));
app.listen(PORT);
// websocket();

app.use((error, req, res, next) => {
  // console.log('Path: ', req.path)
  // console.error('Error: ', error)
  telegram.sendAdmin(error + ' ' + req.path)
  if (!res.headersSent) res.status(500).send(error)
})

// Memory monitoring function
function monitorMemoryUsage() {
  const memoryUsage = process.memoryUsage();
  const memoryUsageMB = {
    rss: Math.round(memoryUsage.rss / 1024 / 1024),
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
    external: Math.round(memoryUsage.external / 1024 / 1024)
  };

  console.log('Memory Usage (MB):', memoryUsageMB);

  // If memory usage is high, attempt cleanup
  if (memoryUsageMB.heapUsed > 1024) { // More than 1GB
    console.log('High memory usage detected, attempting cleanup...');

    // Force garbage collection if node was started with --expose-gc
    if (global.gc) {
      global.gc();
      console.log('Garbage collection completed');
    } else {
      console.log('To enable garbage collection, start node with --expose-gc flag');
    }

    // Notify admin of high memory usage
    telegram.sendAdmin(`High memory usage: ${JSON.stringify(memoryUsageMB)}`);
  }
}

// Monitor memory usage every 5 minutes
setInterval(monitorMemoryUsage, 5 * 60 * 1000);

// Improved error handling
process.on('uncaughtException', function (err) {
  // handle the error safely
  console.log('uncaughtException');
  console.error(err);

  // Log memory usage when exception occurs
  const memoryUsage = process.memoryUsage();
  const memoryUsageMB = {
    rss: Math.round(memoryUsage.rss / 1024 / 1024),
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024)
  };

  // Send detailed error information to admin
  telegram.sendAdmin(`Error: ${err}\nMemory: ${JSON.stringify(memoryUsageMB)}`);

  // Force garbage collection if possible
  if (global.gc) {
    global.gc();
    console.log('Garbage collection completed after exception');
  }
}).on('unhandledRejection', (reason, p) => {
  console.error(reason, 'Unhandled Rejection at Promise', p);
  telegram.sendAdmin(`Unhandled Rejection: ${reason}`);
})


console.log(`SIMPERUM Server Ready at port ${PORT}.`);
console.log(process.env.NODE_ENV || 'production');
