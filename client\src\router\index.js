import { createRouter, createWebHistory } from 'vue-router'
import PublicView from '../pages/Public/PublicView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'public',
      component: PublicView,
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../pages/Auth/LoginView.vue'),
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../pages/Dashboard/Dashboard.vue'),
    },
    {
      path: '/responsive-layout',
      name: 'responsive-layout',
      component: () => import('../pages/Dashboard/ResponsiveLayout.vue'),
    },
    {
      path: '/change-password',
      name: 'change-password',
      component: () => import('../pages/Auth/ChangePassword.vue'),
    },
    {
      path: '/Main/Admin/Company',
      name: 'data-dinas',
      component: () => import('../pages/Admin/DataDinas.vue'),
    },
  ],
})

export default router
