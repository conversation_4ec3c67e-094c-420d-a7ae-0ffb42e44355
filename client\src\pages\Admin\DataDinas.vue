<template>
  <Page title="Data Dinas" subtitle="Kelola data dinas dan instansi" :hideSidePanel="true">
    <template #toolbar>
      <Button
        icon="pi pi-plus"
        label="Tambah Dinas"
        severity="success"
        @click="showAddDialog"
      />
    </template>

    <!-- Main Content -->
    <div class="space-y-6">
      <!-- Data Table -->
      <Card>
        <template #content>
          <DataTable
            :value="companies"
            :loading="loading"
            paginator
            :rows="10"
            :rowsPerPageOptions="[5, 10, 20, 50]"
            tableStyle="min-width: 50rem"
            paginatorTemplate="RowsPerPageDropdown FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink"
            currentPageReportTemplate="{first} to {last} of {totalRecords}"
          >
            <template #empty>
              <div class="text-center py-8">
                <i class="pi pi-inbox text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-500">Tidak ada data dinas</p>
              </div>
            </template>

            <Column field="CompanyID" header="ID" sortable style="width: 80px">
              <template #body="{ data }">
                <Badge :value="data.CompanyID" severity="info" />
              </template>
            </Column>

            <Column field="CompanyName" header="Nama Dinas" sortable>
              <template #body="{ data }">
                <div>
                  <div class="font-semibold">{{ data.CompanyName }}</div>
                  <div class="text-sm text-gray-500">{{ data.Description }}</div>
                </div>
              </template>
            </Column>

            <Column field="Address" header="Alamat" sortable>
              <template #body="{ data }">
                <span class="text-gray-600">{{ data.Address || '-' }}</span>
              </template>
            </Column>

            <Column field="Phone" header="Telepon" sortable>
              <template #body="{ data }">
                <span class="text-gray-600">{{ data.Phone || '-' }}</span>
              </template>
            </Column>

            <Column field="Email" header="Email" sortable>
              <template #body="{ data }">
                <span class="text-gray-600">{{ data.Email || '-' }}</span>
              </template>
            </Column>

            <Column header="Aksi" style="width: 120px">
              <template #body="{ data }">
                <div class="flex gap-2">
                  <Button
                    icon="pi pi-pencil"
                    severity="warning"
                    size="small"
                    text
                    rounded
                    @click="editCompany(data)"
                    v-tooltip.top="'Edit'"
                  />
                  <Button
                    icon="pi pi-trash"
                    severity="danger"
                    size="small"
                    text
                    rounded
                    @click="confirmDelete(data)"
                    v-tooltip.top="'Hapus'"
                  />
                </div>
              </template>
            </Column>
          </DataTable>
        </template>
      </Card>
    </div>

    <!-- Add/Edit Dialog -->
    <Dialog
      v-model:visible="showDialog"
      :header="isEdit ? 'Edit Dinas' : 'Tambah Dinas'"
      modal
      :style="{ width: '600px' }"
      :closable="true"
    >
      <form @submit.prevent="saveCompany" class="space-y-4">
        <div class="field">
          <label for="companyName" class="block text-sm font-medium text-gray-700 mb-2">
            Nama Dinas *
          </label>
          <InputText
            id="companyName"
            v-model="form.CompanyName"
            class="w-full"
            :class="{ 'p-invalid': errors.CompanyName }"
            placeholder="Masukkan nama dinas"
          />
          <small v-if="errors.CompanyName" class="p-error">{{ errors.CompanyName }}</small>
        </div>

        <div class="field">
          <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
            Deskripsi
          </label>
          <Textarea
            id="description"
            v-model="form.Description"
            class="w-full"
            rows="3"
            placeholder="Masukkan deskripsi dinas"
          />
        </div>

        <div class="field">
          <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
            Alamat
          </label>
          <Textarea
            id="address"
            v-model="form.Address"
            class="w-full"
            rows="2"
            placeholder="Masukkan alamat dinas"
          />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="field">
            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
              Telepon
            </label>
            <InputText
              id="phone"
              v-model="form.Phone"
              class="w-full"
              placeholder="Masukkan nomor telepon"
            />
          </div>

          <div class="field">
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              Email
            </label>
            <InputText
              id="email"
              v-model="form.Email"
              class="w-full"
              type="email"
              placeholder="Masukkan email"
            />
          </div>
        </div>
      </form>

      <template #footer>
        <div class="flex justify-end gap-2">
          <Button
            label="Batal"
            severity="secondary"
            @click="hideDialog"
          />
          <Button
            :label="isEdit ? 'Update' : 'Simpan'"
            :loading="saving"
            @click="saveCompany"
          />
        </div>
      </template>
    </Dialog>

    <!-- Delete Confirmation Dialog -->
    <Dialog
      v-model:visible="showDeleteDialog"
      header="Konfirmasi Hapus"
      modal
      :style="{ width: '400px' }"
    >
      <div class="flex items-center gap-3">
        <i class="pi pi-exclamation-triangle text-orange-500 text-2xl"></i>
        <div>
          <p class="mb-2">Apakah Anda yakin ingin menghapus dinas ini?</p>
          <p class="text-sm text-gray-600">
            <strong>{{ selectedCompany?.CompanyName }}</strong>
          </p>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-2">
          <Button
            label="Batal"
            severity="secondary"
            @click="showDeleteDialog = false"
          />
          <Button
            label="Hapus"
            severity="danger"
            :loading="deleting"
            @click="deleteCompany"
          />
        </div>
      </template>
    </Dialog>
  </Page>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import Page from '@/components/Page.vue'
import { api } from '@/utils/api'

// PrimeVue components
import Button from 'primevue/button'
import Card from 'primevue/card'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Badge from 'primevue/badge'

const toast = useToast()

// Reactive data
const companies = ref([])
const loading = ref(false)
const saving = ref(false)
const deleting = ref(false)
const showDialog = ref(false)
const showDeleteDialog = ref(false)
const isEdit = ref(false)
const selectedCompany = ref(null)

// Form data
const form = ref({
  CompanyID: null,
  CompanyName: '',
  Description: '',
  Address: '',
  Phone: '',
  Email: ''
})

// Form validation errors
const errors = ref({})

// Methods
const loadCompanies = async () => {
  loading.value = true
  try {
    const response = await api.call('Arch_SelCompany')
    if (response.success) {
      companies.value = response.data || []
    } else {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: response.message || 'Gagal memuat data dinas',
        life: 3000
      })
    }
  } catch (error) {
    console.error('Error loading companies:', error)
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Gagal memuat data dinas',
      life: 3000
    })
  } finally {
    loading.value = false
  }
}

const showAddDialog = () => {
  isEdit.value = false
  resetForm()
  showDialog.value = true
}

const editCompany = (company) => {
  isEdit.value = true
  form.value = { ...company }
  showDialog.value = true
}

const resetForm = () => {
  form.value = {
    CompanyID: null,
    CompanyName: '',
    Description: '',
    Address: '',
    Phone: '',
    Email: ''
  }
  errors.value = {}
}

const validateForm = () => {
  errors.value = {}

  if (!form.value.CompanyName?.trim()) {
    errors.value.CompanyName = 'Nama dinas harus diisi'
  }

  return Object.keys(errors.value).length === 0
}

const saveCompany = async () => {
  if (!validateForm()) return

  saving.value = true
  try {
    const response = await api.call('Arch_SavCompany', form.value)
    if (response.success) {
      toast.add({
        severity: 'success',
        summary: 'Berhasil',
        detail: isEdit.value ? 'Data dinas berhasil diupdate' : 'Data dinas berhasil ditambahkan',
        life: 3000
      })
      hideDialog()
      await loadCompanies()
    } else {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: response.message || 'Gagal menyimpan data dinas',
        life: 3000
      })
    }
  } catch (error) {
    console.error('Error saving company:', error)
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Gagal menyimpan data dinas',
      life: 3000
    })
  } finally {
    saving.value = false
  }
}

const confirmDelete = (company) => {
  selectedCompany.value = company
  showDeleteDialog.value = true
}

const deleteCompany = async () => {
  if (!selectedCompany.value) return

  deleting.value = true
  try {
    const response = await api.call('Arch_DekCompany', {
      CompanyID: selectedCompany.value.CompanyID
    })

    if (response.success) {
      toast.add({
        severity: 'success',
        summary: 'Berhasil',
        detail: 'Data dinas berhasil dihapus',
        life: 3000
      })
      showDeleteDialog.value = false
      await loadCompanies()
    } else {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: response.message || 'Gagal menghapus data dinas',
        life: 3000
      })
    }
  } catch (error) {
    console.error('Error deleting company:', error)
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Gagal menghapus data dinas',
      life: 3000
    })
  } finally {
    deleting.value = false
  }
}

const hideDialog = () => {
  showDialog.value = false
  resetForm()
}

// Initialize
onMounted(() => {
  loadCompanies()
})
</script>

<style scoped>
.field {
  margin-bottom: 1rem;
}

.p-error {
  color: #e24c4c;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.p-invalid {
  border-color: #e24c4c;
}
</style>
