<template>
  <div class="flex min-h-screen">
    <!-- Mobile backdrop when menu is expanded -->
    <div
      v-if="isMobile && !isSidebarCollapsed && $route.name !== 'login' && $route.name !== 'public'"
      class="fixed inset-0 bg-black bg-opacity-50 z-30"
      @click="toggleSidebar"
    ></div>

    <!-- Desktop backdrop when menu is expanded (floating over content) -->
    <div
      v-if="!isMobile && !isSidebarCollapsed && $route.name !== 'login' && $route.name !== 'public'"
      class="fixed inset-0 bg-black bg-opacity-20 z-30"
      @click="isSidebarCollapsed = true"
    ></div>

    <!-- Mobile burger menu button -->
    <button
      v-if="isMobile && $route.name !== 'login' && $route.name !== 'public'"
      class="fixed top-4 right-4 z-50 p-2 bg-blue-500 text-white rounded-md"
      @click="toggleSidebar"
    >
      <svg
        class="w-6 h-6"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M4 6h16M4 12h16m-7 6h7"
        ></path>
      </svg>
    </button>

    <!-- Floating Vertical Menu -->
    <VerticalMenu
      v-if="$route.name !== 'login' && $route.name !== 'public'"
      :is-collapsed="isSidebarCollapsed"
      :class="sidebarClasses"
      @mouseenter="handleMenuMouseEnter"
      @mouseleave="handleMenuMouseLeave"
      @menu-interaction="handleMenuInteraction"
      @menu-leave="handleMenuMouseLeave"
    />

    <!-- Main content area -->
    <div :class="mainContentClasses">
      <RouterView />
      <Toast />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import VerticalMenu from './components/Navigation/VerticalMenu.vue'

const isSidebarCollapsed = ref(true)
const route = useRoute() // Used in template as $route
const isMobile = ref(false)
const hoverTimeout = ref(null)

const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
  if (isMobile.value) {
    isSidebarCollapsed.value = true
  }
}

onMounted(() => {
  // authStore.checkAuth()
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
  if (hoverTimeout.value) {
    clearTimeout(hoverTimeout.value)
  }
})

const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
}

// Stable hover handlers with debouncing
const handleMenuMouseEnter = () => {
  if (isMobile.value) return

  if (hoverTimeout.value) {
    clearTimeout(hoverTimeout.value)
  }

  // Immediate expansion for better responsiveness
  isSidebarCollapsed.value = false
}

const handleMenuMouseLeave = () => {
  if (isMobile.value) return

  if (hoverTimeout.value) {
    clearTimeout(hoverTimeout.value)
  }

  // Longer delay before collapsing to allow submenu interaction
  hoverTimeout.value = setTimeout(() => {
    isSidebarCollapsed.value = true
  }, 500) // Increased delay for submenu interaction
}

const handleMenuInteraction = () => {
  if (isMobile.value) return

  // Cancel any pending collapse when user interacts with menu items
  if (hoverTimeout.value) {
    clearTimeout(hoverTimeout.value)
  }

  // Keep menu expanded during interaction
  isSidebarCollapsed.value = false
}

const sidebarClasses = computed(() => {
  const baseClasses = [
    'fixed',
    'top-0',
    'left-0',
    'h-full',
    'transition-all',
    'duration-300',
    'ease-in-out',
  ]

  if (isMobile.value) {
    return [
      ...baseClasses,
      'z-40', // Always high z-index on mobile
      'w-64',
      isSidebarCollapsed.value ? '-translate-x-full' : 'translate-x-0',
    ]
  } else {
    // On desktop:
    // - Collapsed: low z-index (sits beside content, no overlap)
    // - Expanded: high z-index (floats over content)
    const zIndex = isSidebarCollapsed.value ? 'z-10' : 'z-40'
    return [...baseClasses, zIndex, isSidebarCollapsed.value ? 'w-20' : 'w-64']
  }
})

const mainContentClasses = computed(() => {
  const baseClasses = [
    'flex-grow',
    'overflow-auto',
    'transition-all',
    'duration-300',
    'ease-in-out',
  ]

  // When collapsed: add margin so content doesn't overlap with menu
  // When expanded: keep the same margin so content doesn't move (menu floats over)
  if (route.name !== 'login' && route.name !== 'public' && !isMobile.value) {
    return [...baseClasses, 'ml-20'] // Always 20px margin (collapsed menu width)
  }

  return baseClasses
})
</script>

<style scoped></style>
