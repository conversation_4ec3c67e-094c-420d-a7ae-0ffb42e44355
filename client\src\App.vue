<template>
  <div class="flex min-h-screen">
    <!-- Mobile backdrop -->
    <div
      v-if="isMobile && !isSidebarCollapsed && $route.name !== 'login' && $route.name !== 'public'"
      class="fixed inset-0 bg-black bg-opacity-50 z-30"
      @click="toggleSidebar"
    ></div>

    <!-- Desktop backdrop when menu is expanded (floating over content) -->
    <div
      v-if="!isMobile && !isSidebarCollapsed && $route.name !== 'login' && $route.name !== 'public'"
      class="fixed inset-0 bg-black bg-opacity-20 z-30"
      @click="isSidebarCollapsed = true"
    ></div>

    <!-- Mobile burger menu button -->
    <button
      v-if="isMobile && $route.name !== 'login' && $route.name !== 'public'"
      class="fixed top-4 right-4 z-50 p-2 bg-blue-500 text-white rounded-md"
      @click="toggleSidebar"
    >
      <svg
        class="w-6 h-6"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M4 6h16M4 12h16m-7 6h7"
        ></path>
      </svg>
    </button>

    <!-- Floating Vertical Menu -->
    <VerticalMenu
      v-if="$route.name !== 'login' && $route.name !== 'public'"
      :is-collapsed="isSidebarCollapsed"
      :class="sidebarClasses"
      @mouseenter="!isMobile && (isSidebarCollapsed = false)"
      @mouseleave="!isMobile && (isSidebarCollapsed = true)"
    />

    <!-- Main content area -->
    <div :class="mainContentClasses">
      <RouterView />
      <Toast />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import VerticalMenu from './components/Navigation/VerticalMenu.vue'

const isSidebarCollapsed = ref(true)
const route = useRoute() // Used in template as $route
const isMobile = ref(false)

const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
  if (isMobile.value) {
    isSidebarCollapsed.value = true
  }
}

onMounted(() => {
  // authStore.checkAuth()
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})

const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
}

const sidebarClasses = computed(() => {
  const baseClasses = [
    'fixed',
    'top-0',
    'left-0',
    'h-full',
    'transition-all',
    'duration-300',
    'ease-in-out',
  ]

  if (isMobile.value) {
    return [
      ...baseClasses,
      'z-40', // Always high z-index on mobile
      'w-64',
      isSidebarCollapsed.value ? '-translate-x-full' : 'translate-x-0',
    ]
  } else {
    // On desktop: collapsed = low z-index (behind content), expanded = high z-index (over content)
    const zIndex = isSidebarCollapsed.value ? 'z-10' : 'z-40'
    return [...baseClasses, zIndex, isSidebarCollapsed.value ? 'w-20' : 'w-64']
  }
})

const mainContentClasses = computed(() => {
  const baseClasses = [
    'flex-grow',
    'overflow-auto',
    'transition-all',
    'duration-300',
    'ease-in-out',
  ]

  // Content always stays in the same position - no margin changes
  // Menu floats over content when expanded
  return baseClasses
})
</script>

<style scoped></style>
