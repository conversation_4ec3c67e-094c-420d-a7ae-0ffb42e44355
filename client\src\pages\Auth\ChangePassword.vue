<template>
  <Toast />
  <div class="change-password-container">
    <div class="change-password-wrapper">
      <!-- Header Section -->
      <div class="header-section">
        <Button
          icon="pi pi-arrow-left"
          label="Kembali"
          class="back-button"
          text
          @click="goBack"
        />
        <h1 class="page-title">Ubah Password</h1>
        <p class="page-subtitle">Perbarui password akun Anda untuk keamanan yang lebih baik</p>
      </div>

      <!-- Change Password Card -->
      <Card class="change-password-card">
        <template #content>
          <div class="change-password-content">
            <form class="change-password-form" @submit.prevent="handleChangePassword">
              <!-- Current Password Input -->
              <div class="form-group">
                <label for="currentPassword" class="form-label">Password Saat Ini</label>
                <Password
                  id="currentPassword"
                  v-model="currentPassword"
                  placeholder="Masukkan password saat ini"
                  class="w-full"
                  toggle-mask
                  required
                  :class="{ 'p-invalid': errors.currentPassword }"
                />
                <small v-if="errors.currentPassword" class="p-error">{{ errors.currentPassword }}</small>
              </div>

              <!-- New Password Input -->
              <div class="form-group">
                <label for="newPassword" class="form-label">Password Baru</label>
                <Password
                  id="newPassword"
                  v-model="newPassword"
                  placeholder="Masukkan password baru"
                  class="w-full"
                  toggle-mask
                  required
                  :class="{ 'p-invalid': errors.newPassword }"
                />
                <small v-if="errors.newPassword" class="p-error">{{ errors.newPassword }}</small>
              </div>

              <!-- Confirm New Password Input -->
              <div class="form-group">
                <label for="confirmPassword" class="form-label">Konfirmasi Password Baru</label>
                <Password
                  id="confirmPassword"
                  v-model="confirmPassword"
                  placeholder="Konfirmasi password baru"
                  class="w-full"
                  toggle-mask
                  required
                  :class="{ 'p-invalid': errors.confirmPassword }"
                />
                <small v-if="errors.confirmPassword" class="p-error">{{ errors.confirmPassword }}</small>
              </div>

              <!-- Password Requirements -->
              <div class="password-requirements">
                <h4 class="requirements-title">Persyaratan Password:</h4>
                <ul class="requirements-list">
                  <li :class="{ 'valid': passwordChecks.length }">
                    <i :class="passwordChecks.length ? 'pi pi-check text-green-500' : 'pi pi-times text-red-500'"></i>
                    Minimal 8 karakter
                  </li>
                  <li :class="{ 'valid': passwordChecks.uppercase }">
                    <i :class="passwordChecks.uppercase ? 'pi pi-check text-green-500' : 'pi pi-times text-red-500'"></i>
                    Mengandung huruf besar
                  </li>
                  <li :class="{ 'valid': passwordChecks.lowercase }">
                    <i :class="passwordChecks.lowercase ? 'pi pi-check text-green-500' : 'pi pi-times text-red-500'"></i>
                    Mengandung huruf kecil
                  </li>
                  <li :class="{ 'valid': passwordChecks.number }">
                    <i :class="passwordChecks.number ? 'pi pi-check text-green-500' : 'pi pi-times text-red-500'"></i>
                    Mengandung angka
                  </li>
                </ul>
              </div>

              <!-- Submit Button -->
              <Button
                type="submit"
                label="Ubah Password"
                class="change-password-button"
                :loading="isLoading"
                :disabled="!isFormValid"
              />
            </form>
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import InputText from 'primevue/inputtext'
import Password from 'primevue/password'
import Button from 'primevue/button'
import Card from 'primevue/card'
import Toast from 'primevue/toast'
import { useToast } from 'primevue/usetoast'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

// Form data
const currentPassword = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const isLoading = ref(false)
const errors = ref({})

// Password validation
const passwordChecks = computed(() => ({
  length: newPassword.value.length >= 8,
  uppercase: /[A-Z]/.test(newPassword.value),
  lowercase: /[a-z]/.test(newPassword.value),
  number: /\d/.test(newPassword.value)
}))

const isPasswordValid = computed(() => {
  return Object.values(passwordChecks.value).every(check => check)
})

const isFormValid = computed(() => {
  return currentPassword.value &&
         newPassword.value &&
         confirmPassword.value &&
         isPasswordValid.value &&
         newPassword.value === confirmPassword.value
})

// Watch for password confirmation
watch([newPassword, confirmPassword], () => {
  errors.value = {}
  if (confirmPassword.value && newPassword.value !== confirmPassword.value) {
    errors.value.confirmPassword = 'Password konfirmasi tidak cocok'
  }
})

// Handle form submission
const handleChangePassword = async () => {
  isLoading.value = true
  errors.value = {}

  try {
    await authStore.updatePassword(currentPassword.value, newPassword.value)

    toast.add({
      severity: 'success',
      summary: 'Berhasil',
      detail: 'Password berhasil diubah',
      life: 3000
    })

    // Redirect back after successful change
    setTimeout(() => {
      router.go(-1)
    }, 1500)

  } catch (error) {
    errors.value.currentPassword = error.message
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error.message,
      life: 3000
    })
  } finally {
    isLoading.value = false
  }
}

// Navigate back
const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.change-password-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #fff 0%, #f3f3f3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
}

.change-password-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fimg.freepik.com%2Fpremium-photo%2Fphoto-modern-home-3d-design_763111-13992.jpg&f=1&nofb=1&ipt=b8464edcb1da416d3970d874bcae853ba9a46483c6707d597d686c787fd47103');
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  z-index: 0;
}

.change-password-wrapper {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 500px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.header-section {
  text-align: center;
  color: #333;
}

.back-button {
  margin-bottom: 1rem;
  align-self: flex-start;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 300;
  margin: 0 0 0.5rem 0;
  letter-spacing: 0.5px;
  color: #333;
}

.page-subtitle {
  font-size: 1rem;
  opacity: 0.8;
  margin: 0;
  font-weight: 300;
  color: #666;
}

.change-password-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.change-password-content {
  padding: 2rem;
}

.change-password-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #333;
  font-size: 0.95rem;
}

.password-requirements {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.requirements-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: #495057;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.requirements-list li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #6c757d;
}

.requirements-list li.valid {
  color: #28a745;
}

.change-password-button {
  background: linear-gradient(135deg, #8bc34a 0%, #7cb342 100%);
  border: none;
  padding: 0.875rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.change-password-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #7cb342 0%, #689f38 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 195, 74, 0.3);
}

.change-password-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* PrimeVue Component Overrides */
:deep(.p-inputtext) {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 0.75rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

:deep(.p-inputtext:focus) {
  border-color: #8bc34a;
  box-shadow: 0 0 0 2px rgba(139, 195, 74, 0.2);
}

:deep(.p-password) {
  width: 100%;
}

:deep(.p-password .p-inputtext) {
  border-radius: 8px 0 0 8px;
  width: 100%;
}

:deep(.p-password .p-password-toggle-mask) {
  border-radius: 0 8px 8px 0;
  border-left: none;
  border: 1px solid #e0e0e0;
}

:deep(.p-password .p-password-toggle-mask:hover) {
  border-color: #8bc34a;
}

:deep(.p-invalid) {
  border-color: #e74c3c !important;
}

.p-error {
  color: #e74c3c;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}
</style>
