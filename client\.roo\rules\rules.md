# VueJS
- Use options api instead of composition api
- put the templates section on top, then script section, then style section

# Code Quality Rules

1. Lint Rules:
    - Never disable any lint rules without explicit user approval

2. Styling Guidelines:
    - Use Tailwind CSS classes instead of inline style objects for new markup

# Adding a New Setting

To add a new setting that persists its state, follow the steps in docs/settings.md