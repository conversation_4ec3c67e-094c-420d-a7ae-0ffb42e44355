/**
 * @fileoverview This script provides a Node.js function to decrypt data 
 * that was encrypted using MySQL's AES_ENCRYPT function.
 * It mimics the behavior of MySQL's AES_DECRYPT.
 */

const crypto = require('crypto');

/**
 * Decrypts data using AES-128-ECB, similar to MySQL's AES_DECRYPT.
 * * MySQL's AES functions use a key length of 128 bits and the ECB mode.
 * If the provided key is longer than 16 bytes, only the first 16 bytes are used.
 * If the key is shorter, it's padded with null bytes to 16 bytes.
 * This function replicates that key handling behavior.
 *
 * @param {<PERSON>uffer} encryptedData The encrypted data as a Buffer.
 * @param {string} key The decryption key string.
 * @returns {string} The decrypted data as a UTF-8 string.
 */
function aes_decrypt(encryptedData, key) {
  try {
    // MySQL's AES functions use a 128-bit key.
    // We need to ensure our key is exactly 16 bytes (128 bits).
    const keyBuffer = Buffer.alloc(16, 0); // Create a 16-byte buffer filled with zeros
    keyBuffer.write(key, 0, 'utf8'); // Write the key string to the buffer

    // MySQL uses the 'aes-128-ecb' cipher.
    // ECB mode does not use an Initialization Vector (IV), so we pass an empty string or null.
    const decipher = crypto.createDecipheriv('aes-128-ecb', keyBuffer, null);

    // It's important to disable auto padding as MySQL handles padding itself.
    decipher.setAutoPadding(false);

    // Decrypt the data
    let decrypted = decipher.update(encryptedData, 'binary', 'utf8');
    decrypted += decipher.final('utf8');

    // MySQL pads with null bytes, which we should trim.
    return decrypted.replace(/\0+$/, '');

  } catch (error) {
    console.error("Decryption failed:", error);
    return null;
  }
}

// --- Example Usage ---

// 1. The encrypted data from your example.
const encryptedPayload = {
  "NoRef": {
    "type": "Buffer",
    "data": [58, 140, 102, 136, 131, 44, 83, 88, 59, 209, 21, 5, 222, 12, 118, 114] // Note: Padded to a 16-byte block
  }
};

// Convert the 'data' array into a Node.js Buffer object.
const encryptedBuffer = Buffer.from(encryptedPayload.NoRef.data);

// 2. The key to decrypt the content.
// IMPORTANT: Replace 'your_secret_key' with the actual key used for encryption.
const decryptionKey = 'your_secret_key';

// 3. Call the decryption function.
const decryptedData = aes_decrypt(encryptedBuffer, decryptionKey);

// 4. Display the result.
if (decryptedData !== null) {
  console.log('Encrypted (Buffer):', encryptedBuffer);
  console.log('Decryption Key:', decryptionKey);
  console.log('---');
  console.log('Decrypted Data:', decryptedData);
}

// --- To make this runnable, let's create a piece of data and encrypt it first
// to prove the decryption works with a known key and plaintext.

function deriveMySQLKey(key) {
  // 1. Create a 16-byte buffer initialized with zeros. This will hold the final key.
  const finalKey = Buffer.alloc(16, 0);
  
  // 2. Convert the input key string to a buffer to access its bytes.
  const keyBytes = Buffer.from(key, 'utf8');

  // 3. Fold the key using XOR.
  // For each byte in the input key, XOR it with a byte in the final key.
  // The position in the final key is determined by `index % 16`.
  for (let i = 0; i < keyBytes.length; i++) {
    const keyByte = keyBytes[i];
    const bufferIndex = i % 16;
    finalKey[bufferIndex] = finalKey[bufferIndex] ^ keyByte;
  }

  return finalKey;
}

/**
 * Encrypts data using AES-128-ECB, similar to MySQL's AES_ENCRYPT.
 * @param {string} data The plaintext data to encrypt.
 * @param {string} key The encryption key string.
 * @returns {Buffer} The encrypted data as a Buffer.
 */
function aes_encrypt(data, key) {
  const keyBuffer = deriveMySQLKey(key);

  const cipher = crypto.createCipheriv('aes-128-ecb', keyBuffer, null);
  cipher.setAutoPadding(true); // Auto padding is fine for encryption here

  let encrypted = cipher.update(data, 'utf8', 'binary');
  encrypted += cipher.final('binary');

  return Buffer.from(encrypted, 'binary');
}

console.log('\n\n--- Verification Example ---');
const plaintext = 'This is a test!';
const secretKey = 'secret-key-123';

const encryptedForVerification = aes_encrypt(plaintext, secretKey);
console.log('Plaintext:', plaintext);
console.log('Encrypted for verification:', encryptedForVerification);

const decryptedForVerification = aes_decrypt(Buffer.from([64, 95, 58, 188, 21, 2, 149, 27, 135, 212, 218, 165, 23, 37, 133,
  192, 127, 142, 85, 54, 50, 244, 224, 146, 205, 243, 134, 215, 168, 6, 72, 148]), '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5');
console.log('Decrypted for verification:', decryptedForVerification);
console.log('Verification successful:', plaintext === decryptedForVerification);