#!/bin/bash
#FILE="/home/<USER>/hack.log" 
#NOTIFY="/home/<USER>/hack.notify"
#TOKEN="**********************************************"
#ID="662216470"
#URL="https://api.telegram.org/bot$TOKEN/sendMessage"
#WHITELIST=("************/24" "**********/16" "*********/16")

#!/bin/bash

# --- Configuration ---
LOG_FILE="/home/<USER>/hack.log" # Temporary file for found IPs
TELEGRAM_TOKEN="**********************************************" # Replace with your Telegram Bot Token
TELEGRAM_CHAT_ID="662216470" # Replace with your Telegram Chat ID
IPGEOLOCATION_API_KEY="********************************" # Replace with your ipgeolocation.io API Key
DOCKER_CONTAINER="simperum-frontend-1" # Nginx container name
TELEGRAM_URL="https://api.telegram.org/bot$TELEGRAM_TOKEN/sendMessage"
WHITELIST_SUBNETS=("************/24" "**********/16" "*********/16") # Your Whitelisted /16 Subnets

# --- Dependencies ---
if ! command -v jq &> /dev/null; then
    echo "Error: jq is not installed. Please install jq (e.g., sudo apt install jq)."
    exit 1
fi
if ! command -v curl &> /dev/null; then
    echo "Error: curl is not installed. Please install curl (e.g., sudo apt install curl)."
    exit 1
fi
if ! command -v iptables &> /dev/null; then
    echo "Error: iptables is not installed."
    exit 1
fi

# --- Functions ---
# Checks if an iptables rule already exists for the given SUBNET
# Uses global SUBNET variable
rule_exists() {
    # Check if SUBNET is set before calling iptables
    if [[ -z "$SUBNET" ]]; then
        echo "Error: SUBNET variable not set in rule_exists function."
        return 1 # Consider this an error state
    fi
    # Use -C (check) which returns 0 if rule exists, non-zero otherwise
    sudo iptables -C FORWARD -s "$SUBNET" -j DROP &> /dev/null
    return $? # Return the exit status of iptables -C
}

# --- Main Logic ---

echo "Running Nginx log check for suspicious patterns..."

# Get latest suspicious log entries (overwrite log file)
# Using --tail 100 to limit initial fetch size
sudo docker logs --tail 100 "$DOCKER_CONTAINER" 2>&1 | grep -E "\.php|phpinfo|wp-|GET /dashboard |\.env|mstshash|\\x16|\\x[0-9]{2}\\x[0-9]{2}" > "$LOG_FILE"

# Check if the temporary log file has content
if [[ ! -s "$LOG_FILE" ]]; then
    echo "No suspicious entries found in the last 100 log lines."
    # Optional: truncate file explicitly if needed, though '>' already does
    # > "$LOG_FILE"
    exit 0
fi

echo "Suspicious entries found. Processing unique IPs..."

# Process unique IPs found in the log file
awk '{print $1}' "$LOG_FILE" | sort -u | while IFS= read -r IP_ADDRESS; do
    # Basic check for valid IP format (simple regex)
    if ! [[ "$IP_ADDRESS" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        echo "Skipping invalid IP format: $IP_ADDRESS"
        continue
    fi

    echo "Processing IP: $IP_ADDRESS"

    # Get Geolocation Info
    GEO_JSON=$(curl -s "https://api.ipgeolocation.io/ipgeo?apiKey=${IPGEOLOCATION_API_KEY}&ip=${IP_ADDRESS}&fields=country_name,city")
    COUNTRY_NAME=$(echo "$GEO_JSON" | jq -r ".country_name // \"Unknown\"") # Default to Unknown if null
    CITY_NAME=$(echo "$GEO_JSON" | jq -r ".city // \"Unknown\"")       # Default to Unknown if null
    SOURCE_LOCATION="$CITY_NAME, $COUNTRY_NAME"

    echo "IP $IP_ADDRESS located in: $SOURCE_LOCATION"

    # --- Conditional Subnet Calculation ---
    if [[ "$COUNTRY_NAME" == "Indonesia" ]] || [[ "$IP_ADDRESS" == 103* ]]; then
        # Use /24 subnet for Indonesia
        # SUBNET=$(echo "$IP_ADDRESS" | awk -F'.' '{print $1 "." $2 "." $3 ".0/24"}')
        SUBNET=$(echo "$IP_ADDRESS")
        echo "Country is Indonesia. Using /24 subnet: $SUBNET"
        cp "$LOG_FILE" /home/<USER>/"$IP_ADDRESS".log
    else
        # Use /16 subnet for other countries (original behavior)
        SUBNET=$(echo "$IP_ADDRESS" | awk -F'.' '{print $1 "." $2 ".0.0/16"}')
        echo "Country is $COUNTRY_NAME. Using /16 subnet: $SUBNET"
    fi

    # --- Whitelist Check ---
    whitelisted=0
    for WL_SUBNET in "${WHITELIST_SUBNETS[@]}"; do
        # Comparing calculated subnet against whitelist entries
        if [[ "$SUBNET" == "$WL_SUBNET" ]]; then
            echo "Skipping blocking: Calculated subnet $SUBNET for IP $IP_ADDRESS is whitelisted."
            whitelisted=1
            break # Exit whitelist check loop
        fi
        # NOTE: Add more robust check here if whitelist contains non-/16 ranges
        # e.g., using ipcalc: if command -v ipcalc &>/dev/null && ipcalc -cs "$IP_ADDRESS" "$WL_SUBNET"; then ...
    done

    # If whitelisted, continue to the next IP address in the log file
    if [[ "$whitelisted" -eq 1 ]]; then
        continue
    fi

    # --- Check Rule and Block ---
    # Use the function `rule_exists` which returns 0 if rule exists
    if rule_exists; then
        echo "Rule for $SUBNET already exists. No action needed for IP $IP_ADDRESS."
    else
        echo "Rule for $SUBNET does not exist. Blocking and notifying..."
        # Send Telegram Notification
        NOTIFICATION_TEXT="Suspicious activity detected from IP: ${IP_ADDRESS} (${SOURCE_LOCATION}). Blocking subnet: ${SUBNET}"
        curl -s -X POST "$TELEGRAM_URL" -d chat_id="$TELEGRAM_CHAT_ID" -d text="$NOTIFICATION_TEXT" > /dev/null 2>&1
        echo "Telegram notification sent."

        # Block the subnet using iptables in FORWARD chain
        sudo iptables -I FORWARD 1 -s "$SUBNET" -j DROP
        if [[ $? -eq 0 ]]; then
            echo "Successfully blocked subnet $SUBNET in FORWARD chain."
            # Consider saving rules if using iptables-persistent
            # sudo netfilter-persistent save
        else
            echo "Error blocking subnet $SUBNET."
        fi
    fi

done # End of loop processing unique IPs

# Clean up the temporary log file after processing all IPs
> "$LOG_FILE"
echo "Log processing complete. Temporary log file cleared."

exit 0

