<template>
  <div class="bar-chart-container">
    <div class="chart-header">
      <h3 class="chart-title">{{ title }}</h3>
      <p class="chart-subtitle">{{ subtitle }}</p>
    </div>
    <div class="chart-wrapper">
      <Bar
        :data="chartData"
        :options="chartOptions"
        class="bar-chart"
      />
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { Bar } from 'vue-chartjs'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
)

const props = defineProps({
  title: {
    type: String,
    default: 'Statistics'
  },
  subtitle: {
    type: String,
    default: 'Comparative analysis'
  },
  data: {
    type: Array,
    default: () => [12, 19, 3, 5, 2, 3, 9, 15, 8, 11]
  },
  labels: {
    type: Array,
    default: () => ['Q1', 'Q2', 'Q3', 'Q4', 'Q5', 'Q6', 'Q7', 'Q8', 'Q9', 'Q10']
  },
  colors: {
    type: Array,
    default: () => [
      '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
      '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
    ]
  }
})

const chartData = computed(() => ({
  labels: props.labels,
  datasets: [{
    label: 'Value',
    data: props.data,
    backgroundColor: props.colors.slice(0, props.data.length),
    borderColor: props.colors.slice(0, props.data.length),
    borderWidth: 0,
    borderRadius: 8,
    borderSkipped: false,
    barThickness: 'flex',
    maxBarThickness: 40
  }]
}))

const chartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#ffffff',
      bodyColor: '#ffffff',
      borderColor: '#3B82F6',
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: true,
      padding: 12,
      callbacks: {
        labelColor: function(context) {
          return {
            borderColor: context.dataset.backgroundColor[context.dataIndex],
            backgroundColor: context.dataset.backgroundColor[context.dataIndex]
          }
        }
      }
    }
  },
  scales: {
    x: {
      grid: {
        display: false
      },
      border: {
        display: false
      },
      ticks: {
        color: '#6B7280',
        font: {
          size: 12
        }
      }
    },
    y: {
      grid: {
        color: '#F3F4F6',
        borderDash: [5, 5]
      },
      border: {
        display: false
      },
      ticks: {
        color: '#6B7280',
        font: {
          size: 12
        },
        padding: 10,
        beginAtZero: true
      }
    }
  },
  elements: {
    bar: {
      borderRadius: 8
    }
  }
})
</script>

<style scoped>
.bar-chart-container {
  @apply bg-white rounded-xl p-6 shadow-sm border border-gray-100;
}

.chart-header {
  @apply mb-6;
}

.chart-title {
  @apply text-lg font-semibold text-gray-800 mb-1;
}

.chart-subtitle {
  @apply text-sm text-gray-500;
}

.chart-wrapper {
  @apply relative;
  height: 300px;
}

.bar-chart {
  @apply w-full h-full;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .chart-wrapper {
    height: 250px;
  }
}
</style>
