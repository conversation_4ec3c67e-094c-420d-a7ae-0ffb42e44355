<template>
  <div class="speedometer-container">
    <div class="chart-header">
      <h3 class="chart-title">{{ title }}</h3>
      <p class="chart-subtitle">{{ subtitle }}</p>
    </div>
    <div class="speedometer-wrapper">
      <canvas ref="canvasRef" class="speedometer-canvas"></canvas>
      <div class="speedometer-center">
        <div class="speedometer-value">{{ displayValue }}</div>
        <div class="speedometer-label">{{ valueLabel }}</div>
      </div>
      <!-- Range Labels -->
      <div class="range-labels">
        <div class="range-label range-label-left">{{ minValue }}</div>
        <div class="range-label range-label-right">{{ maxValue }}</div>
        <div class="range-label range-label-top-left">{{ ranges[0].max }}</div>
        <div class="range-label range-label-top-right">{{ ranges[2].max }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, watch } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: 'Performance'
  },
  subtitle: {
    type: String,
    default: 'Current metrics'
  },
  value: {
    type: Number,
    default: 370
  },
  minValue: {
    type: Number,
    default: 350
  },
  maxValue: {
    type: Number,
    default: 850
  },
  valueLabel: {
    type: String,
    default: ''
  },
  ranges: {
    type: Array,
    default: () => [
      { min: 350, max: 600, color: '#EF4444' }, // Red
      { min: 600, max: 700, color: '#F59E0B' }, // Orange/Yellow
      { min: 700, max: 800, color: '#10B981' }, // Green
      { min: 800, max: 850, color: '#6B7280' }  // Gray
    ]
  }
})

const canvasRef = ref(null)

const displayValue = computed(() => {
  return Math.round(props.value)
})

const drawSpeedometer = () => {
  const canvas = canvasRef.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  const rect = canvas.getBoundingClientRect()

  // Set canvas size
  canvas.width = rect.width * window.devicePixelRatio
  canvas.height = rect.height * window.devicePixelRatio
  ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

  const centerX = rect.width / 2
  const centerY = rect.height / 2 + 20
  const radius = Math.min(rect.width, rect.height) / 2 - 40
  const lineWidth = 20

  // Clear canvas
  ctx.clearRect(0, 0, rect.width, rect.height)

  // Draw background arcs for each range
  const totalRange = props.maxValue - props.minValue
  const startAngle = Math.PI * 0.75 // Start at 135 degrees
  const endAngle = Math.PI * 0.25   // End at 45 degrees
  const totalAngle = endAngle - startAngle + Math.PI * 2

  props.ranges.forEach((range) => {
    const rangeStart = (range.min - props.minValue) / totalRange
    const rangeEnd = (range.max - props.minValue) / totalRange

    const arcStartAngle = startAngle + (rangeStart * totalAngle)
    const arcEndAngle = startAngle + (rangeEnd * totalAngle)

    // Draw background arc
    ctx.beginPath()
    ctx.arc(centerX, centerY, radius, arcStartAngle, arcEndAngle)
    ctx.strokeStyle = '#E5E7EB'
    ctx.lineWidth = lineWidth
    ctx.lineCap = 'round'
    ctx.stroke()

    // Draw colored arc based on current value
    if (props.value >= range.min) {
      const valueInRange = Math.min(props.value, range.max)
      const valueEnd = (valueInRange - props.minValue) / totalRange
      const valueEndAngle = startAngle + (valueEnd * totalAngle)

      ctx.beginPath()
      ctx.arc(centerX, centerY, radius, arcStartAngle, Math.min(arcEndAngle, valueEndAngle))
      ctx.strokeStyle = range.color
      ctx.lineWidth = lineWidth
      ctx.lineCap = 'round'
      ctx.stroke()
    }
  })
}

onMounted(() => {
  drawSpeedometer()
  window.addEventListener('resize', drawSpeedometer)
})

watch(() => props.value, () => {
  drawSpeedometer()
})
</script>

<style scoped>
.speedometer-container {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #f3f4f6;
}

.chart-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.chart-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
}

.speedometer-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 240px;
}

.speedometer-canvas {
  width: 100%;
  height: 100%;
}

.speedometer-center {
  position: absolute;
  top: 65%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.speedometer-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

.speedometer-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.range-labels {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.range-label {
  position: absolute;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
}

.range-label-left {
  bottom: 20%;
  left: 15%;
}

.range-label-right {
  bottom: 20%;
  right: 15%;
}

.range-label-top-left {
  top: 25%;
  left: 20%;
}

.range-label-top-right {
  top: 25%;
  right: 20%;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .speedometer-wrapper {
    height: 200px;
  }

  .speedometer-value {
    font-size: 2rem;
  }

  .range-label {
    font-size: 0.625rem;
  }
}
</style>
