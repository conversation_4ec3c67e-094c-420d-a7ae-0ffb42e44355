const nodemailer = require("nodemailer")
const Imap = require('imap');

// sendMail({
//     from: "BP2 Prov Jateng<<EMAIL>>", // sender address
//     to: resdb[0].Email, // receiver email
//     subject: "<PERSON><PERSON><PERSON><PERSON><PERSON> "+resdb[0].NoPengujian, // Subject line
//     text: str,
//     html: str.replaceAll('\n', '<br />'),
//     attachments: [{
//       filename: `sertifikat-${resdb[0].NoPengujian}.pdf`,
//       path: path.join(__dirname, `./sertifikat/${year}/${addf}.pdf`),
//       contentType: 'application/pdf'
//     },{
//       filename: `lembar-kerja-${resdb[0].NoPengujian}.pdf`,
//       path: path.join(__dirname + `/../../${signedFn}`),
//       contentType: 'application/pdf'
//     }]
//   })

const transporter = nodemailer.createTransport({
  service: process.env.EMAIL_SERVICE,
  host: process.env.EMAIL_HOST,
  port: 587,
  secure: false,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

const sendMail = async (mailDetails) => {
  try {
    return await transporter.sendMail(mailDetails)
  } catch (error) {
    console.log(error);
  } 
  return null
};

const imap = new Imap({
  user: process.env.EMAIL_USER,
  password: process.env.EMAIL_PASS,
  host: 'imap.gmail.com',
  port: 993,
  tls: true,
  tlsOptions: { rejectUnauthorized: false }
});

const readMail = (searchCriteria = 'UNSEEN', folder = 'INBOX') => {
  return new Promise((resolve, reject) => {
    const imap = new Imap({
      user: process.env.EMAIL_USER,
      password: process.env.EMAIL_PASS,
      host: 'imap.gmail.com',
      port: 993,
      tls: true,
      tlsOptions: { rejectUnauthorized: false }
    });

    const emails = [];

    imap.once('ready', () => {
      imap.openBox(folder, false, (err, box) => {
        if (err) reject(err);

        // Handle different search criteria formats
        let searchArray;
        if (typeof searchCriteria === 'string') {
          searchArray = [searchCriteria];
        } else if (Array.isArray(searchCriteria)) {
          // Format date for SINCE/BEFORE criteria
          searchArray = [];
          for (let i = 0; i < searchCriteria.length; i++) {
            if (searchCriteria[i] === 'SINCE' || searchCriteria[i] === 'BEFORE') {
              searchArray.push(searchCriteria[i]);
              // Format next item if it's a date
              i++;
              if (i < searchCriteria.length) {
                const date = searchCriteria[i];
                if (date instanceof Date) {
                  // Format date as DD-MMM-YYYY
                  const formatted = date.toLocaleString('en-US', { 
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric'
                  }).replace(/,/g, '');
                  searchArray.push(formatted);
                } else {
                  searchArray.push(date);
                }
              }
            } else {
              searchArray.push(searchCriteria[i]);
            }
          }
        } else {
          searchArray = ['UNSEEN'];
        }

        console.log('Searching emails with criteria:', searchArray);
        imap.search(searchArray, (err, results) => {
          if (err) reject(err);
          if (!results || !results.length) {
            imap.end();
            resolve([]);
            return;
          }

          const f = imap.fetch(results, {
            bodies: ['HEADER.FIELDS (FROM TO SUBJECT DATE)', 'TEXT'],
            struct: true
          });

          f.on('message', (msg, seqno) => {
            const email = {
              seqno,
              header: {},
              body: null,
              date: null,
              subject: null,
              from: null,
              to: null
            };

            msg.on('body', (stream, info) => {
              let buffer = '';
              stream.on('data', (chunk) => {
                buffer += chunk.toString('utf8');
              });

              stream.once('end', () => {
                if (info.which === 'TEXT') {
                  email.body = buffer;
                } else {
                  // Parse header fields
                  const header = Imap.parseHeader(buffer);
                  email.subject = header.subject ? header.subject[0] : null;
                  email.from = header.from ? header.from[0] : null;
                  email.to = header.to ? header.to[0] : null;
                  email.date = header.date ? header.date[0] : null;
                  email.header = header;
                }
              });
            });

            msg.once('end', () => {
              emails.push(email);
            });
          });

          f.once('error', (err) => {
            reject(err);
          });

          f.once('end', () => {
            imap.end();
            resolve(emails);
          });
        });
      });
    });

    imap.once('error', (err) => {
      reject(err);
    });

    imap.connect();
  });
};

module.exports = {
  sendMail,
  readMail
};