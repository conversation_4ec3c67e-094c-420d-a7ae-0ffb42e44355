require('dotenv').config({path: require('path').resolve(__dirname, '../../.env')});
console.log(require('path').resolve(__dirname, '../../.env'))
const fs = require('fs');
const csv = require('csv-parse/sync');
const {stringify} = require('csv-stringify/sync');
const mysql = require('mysql');
const util = require('util');
var readlines = require("n-readlines");

// MySQL Configuration with improved timeout settings
const pool = mysql.createPool({
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '20'),
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '3307'),
  // acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),
  // connectTimeout: parseInt(process.env.DB_CONNECT_TIMEOUT || '60000'),
  // timeout: parseInt(process.env.DB_TIMEOUT || '60000'),
  waitForConnections: true,
  queueLimit: 0
});

// Function to get a connection with retry logic
const getConnection = () => {
  return new Promise((resolve, reject) => {
    pool.getConnection((err, connection) => {
      if (err) {
        console.error('Error getting MySQL connection:', err);
        if (err.code === 'PROTOCOL_CONNECTION_LOST') {
          console.log('Retrying connection in 2 seconds...');
          setTimeout(() => {
            getConnection().then(resolve).catch(reject);
          }, 2000);
        } else {
          reject(err);
        }
      } else {
        // Set session variables to increase timeouts
        connection.query('SET SESSION wait_timeout=300', (err) => {
          if (err) console.error('Error setting wait_timeout:', err);

          connection.query('SET SESSION interactive_timeout=300', (err) => {
            if (err) console.error('Error setting interactive_timeout:', err);
            resolve(connection);
          });
        });
      }
    });
  });
};

// Function to execute a query with retry logic
const executeQuery = async (sql, params, retries = 3) => {
  let connection;
  try {
    connection = await getConnection();
    const query = util.promisify(connection.query).bind(connection);
    return await query(sql, params);
  } catch (error) {
    if (retries > 0 && (error.code === 'PROTOCOL_CONNECTION_LOST' ||
      error.code === 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR' ||
      error.message.includes('timeout'))) {
      console.log(`Query failed, retrying... (${retries} attempts left)`);
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      return executeQuery(sql, params, retries - 1);
    }
    throw error;
  } finally {
    if (connection) connection.release();
  }
};

// Process records in batches
async function run(row) {

  // if (row.Kabupaten < 'KOTA MAGELANG') return

  // console.log(row.NIK, row.Kabupaten, row.Kecamatan, row.Desa, row.TipeData);

  const recs1 = await executeQuery(`select * FROM tmp_1022 where NIK = '${row.NIK}'`);
  if (recs1.length) {
    console.log('Data already exists '+recs1[0].Intervensi)
    if(recs1[0].Intervensi && !recs1[0].Intervensi.match(/Diluar Prioritas/i)) return

    await executeQuery(`UPDATE tmp_1022 SET Intervensi = 'APBD PROV 2025' WHERE NIK = '${rec.NIK}'`)
    return
  }

  const recs2 = await executeQuery(`select * FROM tmp_1022 where NIK2 = '${row.NIK}'`);
  if (recs2.length) {
    console.log('Data already exists on NIK2 '+recs2[0].Intervensi)
    if(recs2[0].Intervensi && !recs2[0].Intervensi.match(/Diluar Prioritas/i)) return

    await executeQuery(`UPDATE tmp_1022 SET Intervensi = 'APBD PROV 2025' WHERE NIK2 = '${rec.NIK}'`)
    return
  }


  const tipeCond = row.TipeData == 115 ? `AND TipeData = 'PBDT 2015'` : `AND TipeData <> 'PBDT 2015'`

  const recs = await executeQuery(`select * FROM tmp_1022
    where Intervensi IS NULL AND NIK2 IS NULL ${tipeCond} AND KodeDagri = ${row.KodeDagri}
    LIMIT 1`);
  
  if (!recs.length) {
    console.log('Data not found\n')
    return
  }

  const rec = recs[0]
  console.log('Switch to: ', rec.NIK, rec.Nama);
  let query = `UPDATE tmp_1022 SET NIK2 = ${row.NIK}, Intervensi = 'APBD PROV 2025', NIKENC2 = AES_ENCRYPT('${row.NIK}', '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5') 
    WHERE NIK = '${rec.NIK}'`
  await executeQuery(query)

  return null;
}

async function main() {
  try {

    // await readCSV()

    const records = await executeQuery(`select CAST(AES_DECRYPT(pb.NIK, '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5') AS CHAR) NIK, pb.Kabupaten, pb.Nama, pb.Alamat, pb.Kecamatan, pb.Kelurahan Desa, pb.KodeWilayah KodeDagri, pp.TipeData from prm_bansos pb 
      join prm_pbdt pp 
      on pb.NIK = pp.NIK
      WHERE pb.Tahun = 2025 AND pb.Sumber = 2 AND pb.Tahapan IS NOT NULL`);

    // const records = [{ Kabupaten: 'KLATEN' }]
    // console.log(records)
    let idx = 1
    for (const row of records) {
      console.log(idx+'/'+records.length, row.NIK, row.Nama, row.Kabupaten, row.Kecamatan, row.Desa, row.TipeData);
      await run(row)
      idx++
    }

    console.log('processed successfully');

    // End MySQL pool
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(0);
    });

    process.exit(0)
  } catch (error) {
    console.error('Error:', error);

    // End MySQL pool on error
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(1);
    });
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('Process interrupted, closing connections...');
  pool.end(err => {
    if (err) console.error('Error closing MySQL pool:', err);
    process.exit(2);
  });
});

main();