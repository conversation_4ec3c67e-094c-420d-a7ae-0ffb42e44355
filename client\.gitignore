# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm/

# Optional REPL history
.node_repl_history

# dotenv environment variables file
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# TypeScript cache
*.tsbuildinfo

# Optional editor directories
.idea/
.vscode/

# Build artifacts
dist/
build/

# Coverage directory
coverage/

# macOS
.DS_Store

# Thumbnails
.thumbnails/

# Windows
Thumbs.db
