<template>
  <div class="card h-full flex flex-col" :class="{ collapsed: isCollapsed }">
    <div class="flex items-center p-4 mb-4">
      <svg
        width="35"
        height="40"
        viewBox="0 0 35 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        :class="{ 'mr-2': !isCollapsed }"
      >
        <path
          d="M25.87 18.05L23.16 17.45L25.27 20.46V29.78L32.49 23.76V13.53L29.18 14.73L25.87 18.04V18.05ZM25.27 35.49L29.18 31.58V27.67L25.27 30.98V35.49ZM20.16 17.14H20.03H20.17H20.16ZM30.1 5.19L34.89 4.81L33.08 12.33L24.1 15.67L30.08 5.2L30.1 5.19ZM5.72 14.74L2.41 13.54V23.77L9.63 29.79V20.47L11.74 17.46L9.03 18.06L5.72 14.75V14.74ZM9.63 30.98L5.72 27.67V31.58L9.63 35.49V30.98ZM4.8 5.2L10.78 15.67L1.81 12.33L0 4.81L4.79 5.19L4.8 5.2ZM24.37 21.05V34.59L22.56 37.29L20.46 39.4H14.44L12.34 37.29L10.53 34.59V21.05L12.42 18.23L17.45 26.8L22.48 18.23L24.37 21.05ZM22.85 0L22.57 0.69L17.45 13.08L12.33 0.69L12.05 0H22.85Z"
          fill="var(--p-primary-color)"
        />
        <path
          d="M30.69 4.21L24.37 4.81L22.57 0.69L22.86 0H26.48L30.69 4.21ZM23.75 5.67L22.66 3.08L18.05 14.24V17.14H19.7H20.03H20.16H20.2L24.1 15.7L30.11 5.19L23.75 5.67ZM4.21002 4.21L10.53 4.81L12.33 0.69L12.05 0H8.43002L4.22002 4.21H4.21002ZM21.9 17.4L20.6 18.2L14.3 17.4L13 17.4L12.4 18.2L12.42 18.23L17.45 26.8L22.48 18.23L22.5 18.2L21.9 17.4ZM4.79002 5.19L10.8 15.7L14.7 17.14H14.74H15.2H16.85V14.24L12.24 3.09L11.15 5.68L4.79002 5.2V5.19Z"
          fill="var(--p-text-color)"
        />
      </svg>
      <span class="text-xl font-semibold" :class="{ hidden: isCollapsed }">Trimzales</span>
    </div>
    <PanelMenu
      :model="menuItems"
      class="flex-grow"
      @mouseenter="$emit('menu-interaction')"
      @mouseleave="$emit('menu-leave')"
    >
      <template #item="{ item, props }">
        <a
          v-ripple
          class="flex items-center p-menuitem-link"
          v-bind="props.action"
          @mouseenter="$emit('menu-interaction')"
        >
          <i :class="item.icon" class="p-menuitem-icon"></i>
          <span :class="{ hidden: isCollapsed }" class="p-menuitem-text">{{ item.label }}</span>
        </a>
      </template>
    </PanelMenu>

    <!-- Profile Section -->
    <div class="profile-section mt-auto border-t border-gray-200 p-4">
      <div class="flex items-center gap-3" :class="{ 'justify-center': isCollapsed }">
        <Avatar
          image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png"
          shape="circle"
          size="normal"
          class="flex-shrink-0"
        />
        <div v-if="!isCollapsed" class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-900 truncate">
            {{ currentUser?.FullName || currentUser?.name || 'User' }}
          </p>
          <p class="text-xs text-gray-500 truncate">
            {{ currentUser?.email || currentUser?.Username || '<EMAIL>' }}
          </p>
        </div>
      </div>

      <!-- Logout Button -->
      <Button
        v-if="!isCollapsed"
        icon="pi pi-sign-out"
        label="Logout"
        severity="danger"
        text
        size="small"
        class="w-full mt-3"
        @click="handleLogout"
      />
      <Button
        v-else
        icon="pi pi-sign-out"
        severity="danger"
        text
        rounded
        size="small"
        class="w-full mt-3"
        v-tooltip.right="'Logout'"
        @click="handleLogout"
      />
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useMenuStore } from '@/stores/menu'
import PanelMenu from 'primevue/panelmenu'
import Avatar from 'primevue/avatar'
import Button from 'primevue/button'

const props = defineProps({
  isCollapsed: Boolean,
})

defineEmits(['menu-interaction', 'menu-leave'])

const router = useRouter()
const authStore = useAuthStore()
const menuStore = useMenuStore()

// Make router available globally for menu navigation
window.router = router

// Computed
const menuItems = computed(() => {
  const transformedItems = menuStore.transformedMenuItems

  // Update command functions to use the current router instance
  const updateCommands = (items, isTopLevel = true) => {
    return items.map((item) => {
      const newItem = { ...item }

      if (newItem.url) {
        newItem.command = () => {
          router.push(newItem.url)
        }
      }

      // When collapsed, remove submenu items to prevent expansion
      if (props.isCollapsed && newItem.items && newItem.items.length > 0) {
        if (isTopLevel) {
          // For top-level items, keep the structure but mark as non-expandable
          newItem.items = []
          newItem.disabled = false
        }
      } else if (newItem.items && newItem.items.length > 0) {
        newItem.items = updateCommands(newItem.items, false)
      }

      return newItem
    })
  }

  return updateCommands([...transformedItems], true)
})

const currentUser = computed(() => authStore.currentUser)

// Methods
const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}

// Initialize menu on component mount
onMounted(() => {
  if (authStore.isLoggedIn && !menuStore.isLoaded) {
    menuStore.loadMenu()
  }
})
</script>

<style scoped>
.card {
  height: 100%; /* Make the card fill the height */
  border-radius: 0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  border-right: 1px solid var(--p-surface-border);
  background-color: var(--p-surface-0) !important;
  backdrop-filter: none;
}

:deep(.p-panelmenu .p-panelmenu-header .p-panelmenu-header-content) {
  border-radius: 0;
  padding: 0.75rem 1rem;
}

:deep(.p-panelmenu .p-panelmenu-content) {
  border-radius: 0;
}

:deep(.p-menuitem-link) {
  padding: 0.75rem 1rem;
}

:deep(.p-menuitem-text) {
  font-size: 0.9rem;
}

:deep(.p-menuitem-icon) {
  font-size: 1.1rem;
  margin-right: 0.75rem;
}

:deep(.p-menuitem-text) {
  transition: opacity 0.3s ease-in-out;
}

.collapsed :deep(.p-menuitem-icon) {
  margin-right: 0 !important;
}

/* Hide all submenu content when collapsed */
.collapsed :deep(.p-panelmenu-content) {
  display: none !important;
}

/* Hide submenu panels when collapsed */
.collapsed :deep(.p-panelmenu-panel .p-panelmenu-panel) {
  display: none !important;
}

/* Hide submenu toggle icons when collapsed */
.collapsed :deep(.p-submenu-icon) {
  display: none !important;
}

/* Hide expanded submenu indicators when collapsed */
.collapsed :deep(.p-panelmenu-header[aria-expanded="true"]) {
  background: transparent !important;
}

/* Ensure only icons are visible for top-level items when collapsed */
.collapsed :deep(.p-panelmenu-header-content) {
  justify-content: center !important;
  padding: 0.75rem 0.5rem !important;
}

/* Profile section styles */
.profile-section {
  background: var(--p-surface-50) !important;
  background-color: var(--p-surface-50) !important;
}

.profile-section .text-gray-900 {
  color: var(--p-text-color);
}

.profile-section .text-gray-500 {
  color: var(--p-text-muted-color);
}

.profile-section .border-gray-200 {
  border-color: var(--p-surface-border);
}
</style>
