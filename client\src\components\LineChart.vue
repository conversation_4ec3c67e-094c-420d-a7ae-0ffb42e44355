<template>
  <div class="line-chart-container">
    <div class="chart-header">
      <h3 class="chart-title">{{ title }}</h3>
      <p class="chart-subtitle">{{ subtitle }}</p>
    </div>
    <div class="chart-wrapper">
      <Line
        :data="chartData"
        :options="chartOptions"
        class="line-chart"
      />
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { Line } from 'vue-chartjs'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

const props = defineProps({
  title: {
    type: String,
    default: 'Trend Analysis'
  },
  subtitle: {
    type: String,
    default: 'Performance over time'
  },
  data: {
    type: Array,
    default: () => [65, 59, 80, 81, 56, 55, 40, 65, 78, 85, 92, 88]
  },
  labels: {
    type: Array,
    default: () => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
  },
  color: {
    type: String,
    default: '#3B82F6'
  },
  fillColor: {
    type: String,
    default: 'rgba(59, 130, 246, 0.1)'
  }
})

const chartData = computed(() => ({
  labels: props.labels,
  datasets: [{
    label: 'Value',
    data: props.data,
    borderColor: props.color,
    backgroundColor: props.fillColor,
    borderWidth: 3,
    fill: true,
    tension: 0.4,
    pointBackgroundColor: props.color,
    pointBorderColor: '#ffffff',
    pointBorderWidth: 2,
    pointRadius: 6,
    pointHoverRadius: 8,
    pointHoverBackgroundColor: props.color,
    pointHoverBorderColor: '#ffffff',
    pointHoverBorderWidth: 3
  }]
}))

const chartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: 'index'
  },
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#ffffff',
      bodyColor: '#ffffff',
      borderColor: props.color,
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: false,
      padding: 12
    }
  },
  scales: {
    x: {
      grid: {
        display: false
      },
      border: {
        display: false
      },
      ticks: {
        color: '#6B7280',
        font: {
          size: 12
        }
      }
    },
    y: {
      grid: {
        color: '#F3F4F6',
        borderDash: [5, 5]
      },
      border: {
        display: false
      },
      ticks: {
        color: '#6B7280',
        font: {
          size: 12
        },
        padding: 10
      }
    }
  },
  elements: {
    point: {
      hoverRadius: 8
    }
  }
})
</script>

<style scoped>
.line-chart-container {
  @apply bg-white rounded-xl p-6 shadow-sm border border-gray-100;
}

.chart-header {
  @apply mb-6;
}

.chart-title {
  @apply text-lg font-semibold text-gray-800 mb-1;
}

.chart-subtitle {
  @apply text-sm text-gray-500;
}

.chart-wrapper {
  @apply relative;
  height: 300px;
}

.line-chart {
  @apply w-full h-full;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .chart-wrapper {
    height: 250px;
  }
}
</style>
