/* Custom color palette for Decorative website */
:root {
  /* Primary colors */
  --color-white: #ffffff;
  --color-off-white: #f8f9fa;
  --color-light-gray: #f0f0f0;
  --color-gray: #888888;
  --color-dark-gray: #666666;
  --color-black: #333333;

  /* Accent colors */
  --color-accent: #8bc34a; /* Light green accent */

  /* Functional colors */
  --color-background: var(--color-white);
  --color-text: var(--color-black);
  --color-text-light: var(--color-gray);
  --color-border: #eeeeee;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;

  /* Container widths */
  --container-width: 1280px;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-weight: normal;
}

html {
  font-size: 16px;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  line-height: 1.6;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Helvetica,
    Arial,
    sans-serif;
  font-size: 1rem;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

p {
  margin-bottom: 1rem;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

button,
input,
select,
textarea {
  font: inherit;
}
