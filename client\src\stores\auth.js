import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { setAuthToken, api } from '@/utils/api'
import { useMenuStore } from './menu'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const isAuthenticated = ref(false)

  // Getters
  const currentUser = computed(() => user.value)
  const isLoggedIn = computed(() => isAuthenticated.value)

  // Actions
  const login = async (username, password) => {
    try {
      const response = await api.login(username, password)

      if (response.success && response.data && response.data.length > 0) {
        const userData = response.data[0]
        user.value = userData
        isAuthenticated.value = true

        // Store in localStorage for persistence
        localStorage.setItem('user', JSON.stringify(userData))
        localStorage.setItem('isAuthenticated', 'true')

        // Load menu after successful login
        const menuStore = useMenuStore()
        await menuStore.loadMenu()

        return { success: true, data: userData }
      } else {
        throw new Error(response.message || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        message: error.message || 'Login failed. Please try again.',
      }
    }
  }

  const logout = async () => {
    try {
      await logoutApi()
      user.value = null
      isAuthenticated.value = false

      // Clear menu data
      const menuStore = useMenuStore()
      menuStore.clearMenu()

      // Clear localStorage
      localStorage.removeItem('user')
      localStorage.removeItem('isAuthenticated')
      localStorage.removeItem('authToken')

      // Note: Router navigation should be handled by the component calling this function
      return { success: true }
    } catch (error) {
      console.error('Logout error:', error)
      // Still clear local state even if API call fails
      user.value = null
      isAuthenticated.value = false

      // Clear menu data
      const menuStore = useMenuStore()
      menuStore.clearMenu()

      localStorage.removeItem('user')
      localStorage.removeItem('isAuthenticated')
      localStorage.removeItem('authToken')

      return { success: false, message: error.message }
    }
  }

  const initializeAuth = () => {
    // Check if user is already logged in (from localStorage)
    const storedUser = localStorage.getItem('user')
    const storedAuth = localStorage.getItem('isAuthenticated')
    const storedToken = localStorage.getItem('authToken')

    if (storedUser && storedAuth === 'true' && storedToken) {
      user.value = JSON.parse(storedUser)
      isAuthenticated.value = true
      setAuthToken(storedToken) // Ensure token is available for API calls

      // Initialize menu data if user is authenticated
      const menuStore = useMenuStore()
      menuStore.initializeMenu()
    }
  }

  const updatePassword = async (currentPassword, newPassword) => {
    // This would typically make an API call to update the password
    // For now, we'll just simulate the process
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Simulate API call
        if (currentPassword === 'wrongpassword') {
          reject(new Error('Current password is incorrect'))
        } else {
          // Update user data if needed
          resolve({ success: true, message: 'Password updated successfully' })
        }
      }, 1000)
    })
  }

  const sendOtp = async (payload) => {
    try {
      const response = await api.post('/api/send-otp', payload, { notify: true })
      return { success: response.success, message: response.message }
    } catch (error) {
      console.error('Send OTP error:', error)
      return { success: false, message: error.message || 'Failed to send OTP.' }
    }
  }

  const loginWithOtp = async (forms) => {
    try {
      const response = await api.login(forms)
      if (response && response.length > 0) {
        const userData = response[0]
        user.value = userData
        isAuthenticated.value = true

        localStorage.setItem('user', JSON.stringify(userData))
        localStorage.setItem('isAuthenticated', 'true')

        const menuStore = useMenuStore()
        await menuStore.loadMenu()

        return { success: true, data: userData }
      } else {
        throw new Error(response.message || 'Login with OTP failed')
      }
    } catch (error) {
      console.error('Login with OTP error:', error)
      return {
        success: false,
        message: error.message || 'Login with OTP failed. Please try again.',
      }
    }
  }

  const fido2AuthenticateBegin = async (username) => {
    try {
      const response = await api.post('/api/fido2/authenticate/begin', { username })
      return response
    } catch (error) {
      console.error('FIDO2 authenticate begin error:', error)
      return { success: false, message: error.message || 'Failed to begin FIDO2 authentication.' }
    }
  }

  const fido2AuthenticateComplete = async (payload) => {
    try {
      const response = await api.post('/api/fido2/authenticate/complete', payload)
      if (response.success && response.data) {
        const userData = response.data
        user.value = userData
        isAuthenticated.value = true

        localStorage.setItem('user', JSON.stringify(userData))
        localStorage.setItem('isAuthenticated', 'true')
        if (response.token) {
          setAuthToken(response.token)
        }

        const menuStore = useMenuStore()
        await menuStore.loadMenu()

        return { success: true, data: userData, token: response.token }
      } else {
        throw new Error(response.message || 'FIDO2 authentication failed')
      }
    } catch (error) {
      console.error('FIDO2 authenticate complete error:', error)
      return {
        success: false,
        message: error.message || 'FIDO2 authentication failed. Please try again.',
      }
    }
  }

  return {
    // State
    user,
    isAuthenticated,
    // Getters
    currentUser,
    isLoggedIn,
    // Actions
    login,
    logout,
    initializeAuth,
    updatePassword,
    sendOtp,
    loginWithOtp,
    fido2AuthenticateBegin,
    fido2AuthenticateComplete,
  }
})
